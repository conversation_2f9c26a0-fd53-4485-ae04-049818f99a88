{
  // Enable format on save for all files
  "editor.formatOnSave": true,
  
  // Organize imports on save
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll": true
  },
  
  // Java-specific settings
  "[java]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "redhat.java"
  },
  
  // Python-specific settings
  "[python]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "ms-python.black-formatter"
  },
  
  // JSON formatting
  "[json]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  
  // XML formatting (for pom.xml)
  "[xml]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "redhat.vscode-xml"
  }
}
