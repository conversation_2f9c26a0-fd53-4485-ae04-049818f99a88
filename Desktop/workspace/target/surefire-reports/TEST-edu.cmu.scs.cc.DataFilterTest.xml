<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="edu.cmu.scs.cc.DataFilterTest" time="0.084" tests="9" errors="0" skipped="0" failures="0">
  <properties>
    <property name="gopherProxySet" value="false"/>
    <property name="awt.toolkit" value="sun.lwawt.macosx.LWCToolkit"/>
    <property name="java.specification.version" value="11"/>
    <property name="sun.cpu.isalist" value=""/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Desktop/workspace/target/test-classes:/Users/<USER>/Desktop/workspace/target/classes:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.2.0/junit-jupiter-engine-5.2.0.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.0.0/apiguardian-api-1.0.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.2.0/junit-platform-engine-1.2.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.2.0/junit-platform-commons-1.2.0.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.1.0/opentest4j-1.1.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.2.0/junit-jupiter-api-5.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-all/1.3/hamcrest-all-1.3.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://openjdk.java.net/"/>
    <property name="user.timezone" value=""/>
    <property name="java.vm.specification.version" value="11"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-11.0.9.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Desktop/workspace/target/surefire/surefirebooter4339838140718688296.jar /Users/<USER>/Desktop/workspace/target/surefire 2025-08-28T19-13-30_681-jvmRun1 surefire17845542553384362319tmp surefire_016537694883273270067tmp"/>
    <property name="http.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|********/8|*.********/8|localhost|*.localhost|local|*.local|***********/16|*.***********/16|*********/4|*.*********/4|240.0.0.0/4|*.240.0.0.0/4"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Desktop/workspace/target/test-classes:/Users/<USER>/Desktop/workspace/target/classes:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.2.0/junit-jupiter-engine-5.2.0.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.0.0/apiguardian-api-1.0.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.2.0/junit-platform-engine-1.2.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.2.0/junit-platform-commons-1.2.0.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.1.0/opentest4j-1.1.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.2.0/junit-jupiter-api-5.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-all/1.3/hamcrest-all-1.3.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2020-10-20"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-11.0.9.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/Desktop/workspace"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.awt.graphicsenv" value="sun.awt.CGraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Desktop/workspace/target/surefire/surefirebooter4339838140718688296.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|********/8|*.********/8|localhost|*.localhost|local|*.local|***********/16|*.***********/16|*********/4|*.*********/4|240.0.0.0/4|*.240.0.0.0/4"/>
    <property name="java.runtime.version" value="11.0.9+7-LTS"/>
    <property name="user.name" value="MartinYang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="10.16"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="java.vendor.version" value="18.9"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/7v/7d1f682s5tjdfmvbzdv6g5m00000gq/T/"/>
    <property name="java.version" value="11.0.9"/>
    <property name="user.dir" value="/Users/<USER>/Desktop/workspace"/>
    <property name="os.arch" value="x86_64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.lwawt.macosx.CPrinterJob"/>
    <property name="sun.os.patch.level" value="unknown"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vm.version" value="11.0.9+7-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="java.class.version" value="55.0"/>
    <property name="socksNonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|********/8|*.********/8|localhost|*.localhost|local|*.local|***********/16|*.***********/16|*********/4|*.*********/4|240.0.0.0/4|*.240.0.0.0/4"/>
  </properties>
  <testcase name="checkDataLength" classname="edu.cmu.scs.cc.DataFilterTest" time="0.03"/>
  <testcase name="sortRecords" classname="edu.cmu.scs.cc.DataFilterTest" time="0.004"/>
  <testcase name="checkAllRules" classname="edu.cmu.scs.cc.DataFilterTest" time="0.004"/>
  <testcase name="checkDomain" classname="edu.cmu.scs.cc.DataFilterTest" time="0.001"/>
  <testcase name="checkPrefix" classname="edu.cmu.scs.cc.DataFilterTest" time="0.001"/>
  <testcase name="checkSuffix" classname="edu.cmu.scs.cc.DataFilterTest" time="0"/>
  <testcase name="checkFirstLetter" classname="edu.cmu.scs.cc.DataFilterTest" time="0.002"/>
  <testcase name="getColumns" classname="edu.cmu.scs.cc.DataFilterTest" time="0.002"/>
  <testcase name="checkSpecialPage" classname="edu.cmu.scs.cc.DataFilterTest" time="0.002"/>
</testsuite>