<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Main.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-filter</a> &gt; <a href="index.source.html" class="el_package">edu.cmu.scs.cc</a> &gt; <span class="el_source">Main.java</span></div><h1>Main.java</h1><pre class="source lang-java linenums">package edu.cmu.scs.cc;

import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.LinkedList;
import java.util.TreeMap;
import java.util.Map.Entry;
import java.nio.charset.StandardCharsets;

<span class="nc" id="L14">public class Main {</span>

    /**
     * Main entry.
     *
     * &lt;p&gt;
     * The main method reads from {@link System#in}
     * and writes to `output`.
     *
     * &lt;p&gt;
     * I/O must be encoding-aware instead of relying on the system default
     * encoding. {@link java.io.OutputStreamWriter(OutputStream)} and
     * {@link java.io.InputStreamReader(InputStream)} below are encoding-naive.
     * You need to replace them with their encoding-aware constructor
     * counterparts.
     *
     * @param args command-line args
     * @throws IOException if IO error occurs.
     */
    public static void main(final String[] args) throws IOException {
        // TODO: modify and make the try-with-resources statement encoding aware
<span class="nc" id="L35">        try (PrintWriter printWriter = new PrintWriter(new OutputStreamWriter(</span>
                new FileOutputStream(&quot;output&quot;), StandardCharsets.UTF_8), true);
<span class="nc" id="L37">                BufferedReader br = new BufferedReader(</span>
                        new InputStreamReader(System.in, StandardCharsets.UTF_8))) {
            // do not change the code below
<span class="nc" id="L40">            TreeMap&lt;String, Integer&gt; pageviewMap = new TreeMap&lt;&gt;();</span>
            String page;
<span class="nc bnc" id="L42" title="All 2 branches missed.">            while ((page = br.readLine()) != null) {</span>
<span class="nc" id="L43">                String[] columns = DataFilter.getColumns(page);</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">                if (DataFilter.checkAllRules(columns)) {</span>
                    try {
<span class="nc" id="L46">                        pageviewMap.put(columns[DataFilter.TITLE],</span>
<span class="nc" id="L47">                                pageviewMap.getOrDefault(columns[DataFilter.TITLE], 0)</span>
<span class="nc" id="L48">                                        + Integer.parseInt(columns[DataFilter.ACCESS]));</span>
<span class="nc" id="L49">                    } catch (NumberFormatException e) {</span>
                        // ignore it
<span class="nc" id="L51">                    }</span>
                }
<span class="nc" id="L53">            }</span>
<span class="nc" id="L54">            LinkedList&lt;Entry&lt;String, Integer&gt;&gt; linkedList = DataFilter.sortRecords(pageviewMap);</span>
<span class="nc bnc" id="L55" title="All 2 branches missed.">            for (Entry&lt;String, Integer&gt; entry : linkedList) {</span>
<span class="nc" id="L56">                printWriter.print(entry.getKey() + &quot;\t&quot; + entry.getValue() + &quot;\n&quot;);</span>
<span class="nc" id="L57">            }</span>
        }
<span class="nc" id="L59">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.2.201808211720</span></div></body></html>