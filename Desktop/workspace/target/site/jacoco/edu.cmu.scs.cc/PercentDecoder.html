<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PercentDecoder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-filter</a> &gt; <a href="index.html" class="el_package">edu.cmu.scs.cc</a> &gt; <span class="el_class">PercentDecoder</span></div><h1>PercentDecoder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4 of 108</td><td class="ctr2">96%</td><td class="bar">8 of 24</td><td class="ctr2">66%</td><td class="ctr1">8</td><td class="ctr2">14</td><td class="ctr1">2</td><td class="ctr2">25</td><td class="ctr1">0</td><td class="ctr2">2</td></tr></tfoot><tbody><tr><td id="a0"><a href="PercentDecoder.java.html#L72" class="el_method">decode(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="116" height="10" title="74" alt="74"/></td><td class="ctr2" id="c0">97%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">66%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i0">18</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="PercentDecoder.java.html#L55" class="el_method">getHexValue(byte)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="47" height="10" title="30" alt="30"/></td><td class="ctr2" id="c1">93%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">66%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">7</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.2.201808211720</span></div></body></html>