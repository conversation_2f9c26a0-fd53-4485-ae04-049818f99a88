<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataFilter</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-filter</a> &gt; <a href="index.html" class="el_package">edu.cmu.scs.cc</a> &gt; <span class="el_class">DataFilter</span></div><h1>DataFilter</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">12 of 455</td><td class="ctr2">97%</td><td class="bar">6 of 48</td><td class="ctr2">87%</td><td class="ctr1">6</td><td class="ctr2">35</td><td class="ctr1">6</td><td class="ctr2">50</td><td class="ctr1">0</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a3"><a href="DataFilter.java.html#L295" class="el_method">checkFirstLetter(String[])</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="25" alt="25"/></td><td class="ctr2" id="c10">86%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">75%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h0">2</td><td class="ctr2" id="i0">7</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="DataFilter.java.html#L249" class="el_method">checkPrefix(String[])</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="33" alt="33"/></td><td class="ctr2" id="c6">94%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="5" alt="5"/></td><td class="ctr2" id="e3">83%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">7</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="DataFilter.java.html#L268" class="el_method">checkSuffix(String[])</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="33" alt="33"/></td><td class="ctr2" id="c7">94%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="5" alt="5"/></td><td class="ctr2" id="e4">83%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">7</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="DataFilter.java.html#L226" class="el_method">checkSpecialPage(String[])</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="32" alt="32"/></td><td class="ctr2" id="c8">94%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="5" alt="5"/></td><td class="ctr2" id="e5">83%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="DataFilter.java.html#L212" class="el_method">checkDomain(String[])</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="19" alt="19"/></td><td class="ctr2" id="c9">90%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="5" alt="5"/></td><td class="ctr2" id="e6">83%</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="DataFilter.java.html#L73" class="el_method">static {...}</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="234" alt="234"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="DataFilter.java.html#L187" class="el_method">checkAllRules(String[])</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="22" alt="22"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="DataFilter.java.html#L145" class="el_method">lambda$sortRecords$0(Map.Entry, Map.Entry)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="21" alt="21"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a9"><a href="DataFilter.java.html#L143" class="el_method">sortRecords(TreeMap)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="11" alt="11"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="DataFilter.java.html#L202" class="el_method">checkDataLength(String[])</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="8" alt="8"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a7"><a href="DataFilter.java.html#L167" class="el_method">getColumns(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.2.201808211720</span></div></body></html>