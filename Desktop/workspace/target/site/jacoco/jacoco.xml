<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="data-filter"><sessioninfo id="MacBook-Pro-85.local-d905612d" start="1756433612549" dump="1756433614152"/><package name="edu/cmu/scs/cc"><class name="edu/cmu/scs/cc/PercentDecoder" sourcefilename="PercentDecoder.java"><method name="getHexValue" desc="(B)I" line="55"><counter type="INSTRUCTION" missed="2" covered="30"/><counter type="BRANCH" missed="4" covered="8"/><counter type="LINE" missed="1" covered="6"/><counter type="COMPLEXITY" missed="4" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="decode" desc="(Ljava/lang/String;)Ljava/lang/String;" line="72"><counter type="INSTRUCTION" missed="2" covered="74"/><counter type="BRANCH" missed="4" covered="8"/><counter type="LINE" missed="1" covered="17"/><counter type="COMPLEXITY" missed="4" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="4" covered="104"/><counter type="BRANCH" missed="8" covered="16"/><counter type="LINE" missed="2" covered="23"/><counter type="COMPLEXITY" missed="8" covered="6"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="edu/cmu/scs/cc/Main" sourcefilename="Main.java"><method name="&lt;init&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="main" desc="([Ljava/lang/String;)V" line="35"><counter type="INSTRUCTION" missed="93" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="17" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="96" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="18" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="edu/cmu/scs/cc/DataFilter" sourcefilename="DataFilter.java"><method name="sortRecords" desc="(Ljava/util/TreeMap;)Ljava/util/LinkedList;" line="143"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getColumns" desc="(Ljava/lang/String;)[Ljava/lang/String;" line="167"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkAllRules" desc="([Ljava/lang/String;)Z" line="187"><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="BRANCH" missed="0" covered="12"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="7"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkDataLength" desc="([Ljava/lang/String;)Z" line="202"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkDomain" desc="([Ljava/lang/String;)Z" line="212"><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="BRANCH" missed="0" covered="6"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkSpecialPage" desc="([Ljava/lang/String;)Z" line="226"><counter type="INSTRUCTION" missed="0" covered="34"/><counter type="BRANCH" missed="0" covered="6"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkPrefix" desc="([Ljava/lang/String;)Z" line="249"><counter type="INSTRUCTION" missed="0" covered="35"/><counter type="BRANCH" missed="0" covered="6"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkSuffix" desc="([Ljava/lang/String;)Z" line="268"><counter type="INSTRUCTION" missed="0" covered="35"/><counter type="BRANCH" missed="0" covered="6"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkFirstLetter" desc="([Ljava/lang/String;)Z" line="295"><counter type="INSTRUCTION" missed="0" covered="29"/><counter type="BRANCH" missed="0" covered="8"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="5"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$sortRecords$0" desc="(Ljava/util/Map$Entry;Ljava/util/Map$Entry;)I" line="145"><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="73"><counter type="INSTRUCTION" missed="0" covered="234"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="455"/><counter type="BRANCH" missed="0" covered="48"/><counter type="LINE" missed="0" covered="50"/><counter type="COMPLEXITY" missed="0" covered="35"/><counter type="METHOD" missed="0" covered="11"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="Main.java"><line nr="14" mi="3" ci="0" mb="0" cb="0"/><line nr="35" mi="13" ci="0" mb="0" cb="0"/><line nr="37" mi="9" ci="0" mb="0" cb="0"/><line nr="40" mi="4" ci="0" mb="0" cb="0"/><line nr="42" mi="5" ci="0" mb="2" cb="0"/><line nr="43" mi="3" ci="0" mb="0" cb="0"/><line nr="44" mi="3" ci="0" mb="2" cb="0"/><line nr="46" mi="11" ci="0" mb="0" cb="0"/><line nr="47" mi="8" ci="0" mb="0" cb="0"/><line nr="48" mi="2" ci="0" mb="0" cb="0"/><line nr="49" mi="1" ci="0" mb="0" cb="0"/><line nr="51" mi="1" ci="0" mb="0" cb="0"/><line nr="53" mi="1" ci="0" mb="0" cb="0"/><line nr="54" mi="3" ci="0" mb="0" cb="0"/><line nr="55" mi="10" ci="0" mb="2" cb="0"/><line nr="56" mi="17" ci="0" mb="0" cb="0"/><line nr="57" mi="1" ci="0" mb="0" cb="0"/><line nr="59" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="96" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="18" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="DataFilter.java"><line nr="73" mi="0" ci="135" mb="0" cb="0"/><line nr="109" mi="0" ci="83" mb="0" cb="0"/><line nr="123" mi="0" ci="16" mb="0" cb="0"/><line nr="143" mi="0" ci="6" mb="0" cb="0"/><line nr="144" mi="0" ci="3" mb="0" cb="0"/><line nr="145" mi="0" ci="9" mb="0" cb="0"/><line nr="146" mi="0" ci="2" mb="0" cb="2"/><line nr="147" mi="0" ci="2" mb="0" cb="0"/><line nr="149" mi="0" ci="8" mb="0" cb="0"/><line nr="152" mi="0" ci="2" mb="0" cb="0"/><line nr="167" mi="0" ci="5" mb="0" cb="0"/><line nr="187" mi="0" ci="5" mb="0" cb="2"/><line nr="188" mi="0" ci="3" mb="0" cb="2"/><line nr="189" mi="0" ci="3" mb="0" cb="2"/><line nr="190" mi="0" ci="3" mb="0" cb="2"/><line nr="191" mi="0" ci="3" mb="0" cb="2"/><line nr="192" mi="0" ci="5" mb="0" cb="2"/><line nr="202" mi="0" ci="8" mb="0" cb="2"/><line nr="212" mi="0" ci="3" mb="0" cb="2"/><line nr="213" mi="0" ci="2" mb="0" cb="0"/><line nr="215" mi="0" ci="4" mb="0" cb="0"/><line nr="216" mi="0" ci="12" mb="0" cb="4"/><line nr="226" mi="0" ci="4" mb="0" cb="2"/><line nr="227" mi="0" ci="2" mb="0" cb="0"/><line nr="229" mi="0" ci="4" mb="0" cb="0"/><line nr="230" mi="0" ci="16" mb="0" cb="2"/><line nr="231" mi="0" ci="4" mb="0" cb="2"/><line nr="232" mi="0" ci="2" mb="0" cb="0"/><line nr="235" mi="0" ci="2" mb="0" cb="0"/><line nr="249" mi="0" ci="4" mb="0" cb="2"/><line nr="250" mi="0" ci="2" mb="0" cb="0"/><line nr="252" mi="0" ci="5" mb="0" cb="0"/><line nr="253" mi="0" ci="16" mb="0" cb="2"/><line nr="254" mi="0" ci="4" mb="0" cb="2"/><line nr="255" mi="0" ci="2" mb="0" cb="0"/><line nr="258" mi="0" ci="2" mb="0" cb="0"/><line nr="268" mi="0" ci="4" mb="0" cb="2"/><line nr="269" mi="0" ci="2" mb="0" cb="0"/><line nr="271" mi="0" ci="5" mb="0" cb="0"/><line nr="272" mi="0" ci="16" mb="0" cb="2"/><line nr="273" mi="0" ci="4" mb="0" cb="2"/><line nr="274" mi="0" ci="2" mb="0" cb="0"/><line nr="277" mi="0" ci="2" mb="0" cb="0"/><line nr="295" mi="0" ci="4" mb="0" cb="2"/><line nr="296" mi="0" ci="2" mb="0" cb="0"/><line nr="298" mi="0" ci="4" mb="0" cb="0"/><line nr="299" mi="0" ci="3" mb="0" cb="2"/><line nr="300" mi="0" ci="2" mb="0" cb="0"/><line nr="302" mi="0" ci="4" mb="0" cb="0"/><line nr="303" mi="0" ci="10" mb="0" cb="4"/><counter type="INSTRUCTION" missed="0" covered="455"/><counter type="BRANCH" missed="0" covered="48"/><counter type="LINE" missed="0" covered="50"/><counter type="COMPLEXITY" missed="0" covered="35"/><counter type="METHOD" missed="0" covered="11"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="PercentDecoder.java"><line nr="55" mi="0" ci="6" mb="1" cb="3"/><line nr="56" mi="0" ci="4" mb="0" cb="0"/><line nr="57" mi="0" ci="6" mb="1" cb="3"/><line nr="58" mi="0" ci="4" mb="0" cb="0"/><line nr="59" mi="0" ci="6" mb="2" cb="2"/><line nr="60" mi="0" ci="4" mb="0" cb="0"/><line nr="62" mi="2" ci="0" mb="0" cb="0"/><line nr="72" mi="0" ci="2" mb="1" cb="1"/><line nr="73" mi="2" ci="0" mb="0" cb="0"/><line nr="76" mi="0" ci="4" mb="0" cb="0"/><line nr="77" mi="0" ci="3" mb="0" cb="0"/><line nr="78" mi="0" ci="3" mb="0" cb="0"/><line nr="80" mi="0" ci="2" mb="0" cb="0"/><line nr="81" mi="0" ci="2" mb="0" cb="0"/><line nr="82" mi="0" ci="3" mb="0" cb="2"/><line nr="83" mi="0" ci="3" mb="0" cb="0"/><line nr="84" mi="0" ci="6" mb="0" cb="0"/><line nr="85" mi="0" ci="5" mb="0" cb="2"/><line nr="86" mi="0" ci="5" mb="1" cb="1"/><line nr="87" mi="0" ci="7" mb="0" cb="0"/><line nr="89" mi="0" ci="7" mb="0" cb="0"/><line nr="91" mi="0" ci="4" mb="2" cb="2"/><line nr="92" mi="0" ci="9" mb="0" cb="0"/><line nr="94" mi="0" ci="1" mb="0" cb="0"/><line nr="99" mi="0" ci="8" mb="0" cb="0"/><counter type="INSTRUCTION" missed="4" covered="104"/><counter type="BRANCH" missed="8" covered="16"/><counter type="LINE" missed="2" covered="23"/><counter type="COMPLEXITY" missed="8" covered="6"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="100" covered="559"/><counter type="BRANCH" missed="14" covered="64"/><counter type="LINE" missed="20" covered="73"/><counter type="COMPLEXITY" missed="13" covered="41"/><counter type="METHOD" missed="2" covered="13"/><counter type="CLASS" missed="1" covered="2"/></package><counter type="INSTRUCTION" missed="100" covered="559"/><counter type="BRANCH" missed="14" covered="64"/><counter type="LINE" missed="20" covered="73"/><counter type="COMPLEXITY" missed="13" covered="41"/><counter type="METHOD" missed="2" covered="13"/><counter type="CLASS" missed="1" covered="2"/></report>