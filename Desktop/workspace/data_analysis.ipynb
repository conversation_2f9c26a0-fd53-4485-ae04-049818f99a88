{"cells": [{"cell_type": "markdown", "metadata": {"tags": ["excluded_from_script"]}, "source": ["# Data Analysis\n", "\n", "Before you start, make sure that you are familiar with the basic usage of Jupyter Notebook. \n", "\n", "If not, please finish the Jupyter Notebook primer first. Within that primer you can find links to some starter notebooks hosted on Google Colab that will help you practice Linux, Bash, and Pandas fundamentals with **worked examples**.\n", "\n", "In this task, you need to implement the following methods:\n", "```\n", "load_data_to_series\n", "q6\n", "q7\n", "q8\n", "q9\n", "```\n", "\n", "Please implement the `load_data_to_series` method first. You can check the output for each question by executing the cell below the question.\n", "\n", "More cells may be added to the notebook. If you don't want to include the cell in the converted script, please tag the cell with `excluded_from_script`. You can display the tags for each cell in Jupyter Notebook: `View > Cell Toolbar > Tags`.\n", "\n", "Execute `./runner.sh` in the console to check the result. Please make sure that the virtualenv is activated when `runner.sh` runs.\n", "\n", "Finally, remember the write-up section regarding encoding awareness and ensure that you practice those concepts when completing the required questions within this notebook.\n", "\n", "# Pandas\n", "\n", "Pandas is a Python library for practical and real-world data analysis. It provides fast, flexible, and expressive data structures to make it easy to work with data. \n", "\n", "There are two primary data structures provided by Pandas, Series (1-dimensional) and DataFrame (2-dimensional). This week you will start with the Series. And you will practice with the DataFrame next week.\n", "\n", "# Series\n", "\n", "A Series is a one-dimensional labeled array that can hold any data type, integer, floating point number, Python objects, etc. In this task, you will load the filtered Wikipedia output into a Series, where the page title is the label and view count is the data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sys\n", "import argparse"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_data_to_series(input_file):\n", "    \"\"\"\n", "    Load the input file into a Series.\n", "    Please read the documentation of the method `pandas.read_csv`:\n", "    https://pandas.pydata.org/pandas-docs/stable/generated/pandas.read_csv.html?highlight=index_col\n", "\n", "    The default behavior of read_csv will infer the column names using the first line of the input file.\n", "    In the provided Wikipedia dataset, the first line of the input file is not the column names; \n", "    hence, you need to change this default behavior.\n", "    \n", "    Hint: \n", "    1. How to read a TSV file using the read_csv method by specifying the delimiter\n", "    2. How to not infer the first line as the column names\n", "    3. How to read the data into a Series instead of a Dataframe\n", "    4. How to specify the column to be used as the row labels\n", "    \n", "    :param input_file: the path to the input file\n", "    :return: the Series\n", "    \"\"\"\n", "    \n", "    # TODO: Load the input_file in to a Series\n", "    raise NotImplementedError(\"To be implemented\")\n", "    series = None\n", "    \n", "    return series"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def q6():\n", "    \"\"\"\n", "    Print a small sample of a Series as CSV\n", "    \n", "    To view the top n records, read the documentation:\n", "    https://pandas.pydata.org/pandas-docs/stable/basics.html#head-and-tail\n", "    \n", "    output format:\n", "    <page title>,<page view>\n", "    <page title>,<page view>\n", "    <page title>,<page view>\n", "    ...\n", "    \n", "    \"\"\"\n", "    \n", "    # read the output into series\n", "    series = load_data_to_series(\"output\")\n", "    \n", "    # TODO: replace \"None\" with your implementation to select the first 10 records\n", "    raise NotImplementedError(\"To be implemented\")\n", "    res = None\n", "    \n", "    # print the result to standard output in the CSV format\n", "    res.to_csv(sys.stdout, header=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["excluded_from_script"]}, "outputs": [], "source": ["q6()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def q7():\n", "    \"\"\"\n", "    Get values by index label\n", "    \n", "    Since the page title is the label of the Series, you can get the page view by page title.\n", "    Please read the documentation:\n", "    https://pandas.pydata.org/pandas-docs/stable/dsintro.html#series-is-dict-like\n", "    \n", "    output format:\n", "    <page view>\n", "    \"\"\"\n", "    \n", "    # read the output into a Series \n", "    series = load_data_to_series(\"output\")\n", "    \n", "    # TODO: replace \"None\" with your implementation to select the row with the title \"Cloud_computing\"\n", "    raise NotImplementedError(\"To be implemented\")\n", "    res = None\n", "    \n", "    # print the result to standard output\n", "    print(res)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["excluded_from_script"]}, "outputs": [], "source": ["q7()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def q8():\n", "    \"\"\"\n", "    Generates descriptive statistics of a Series\n", "    \n", "    Please read the documentation of the Series and find the method to show all the descriptive statistics.\n", "    https://pandas.pydata.org/pandas-docs/stable/generated/pandas.Series.html?highlight=descriptive\n", "    \n", "    output format:\n", "    \n", "    count,<number>\n", "    mean,<number>\n", "    std,<number>\n", "    min,<number>\n", "    25%,<number>\n", "    50%,<number>\n", "    75%,<number>\n", "    max,<number>\n", "    \n", "    \"\"\"\n", "    \n", "    # read the output into a Series\n", "    series = load_data_to_series(\"output\")\n", "    \n", "    # TODO: generate the descriptive statistics (replace \"None\" with your implementation)\n", "    raise NotImplementedError(\"To be implemented\")\n", "    res = None\n", "    \n", "    # print the result to standard output in csv format\n", "    res.to_csv(sys.stdout, encoding='utf-8', header=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["excluded_from_script"]}, "outputs": [], "source": ["q8()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def q9():\n", "    \"\"\"\n", "    Data filtering in Series\n", "    \n", "    Boolean indexing can be used to filtering data in a Series.\n", "    https://pandas.pydata.org/pandas-docs/stable/indexing.html#boolean-indexing\n", "    \n", "    output format:\n", "    <page title>,<page view>\n", "    <page title>,<page view>\n", "    <page title>,<page view>\n", "    ...\n", "    \n", "    \"\"\"\n", "    \n", "    # read the output into a Series\n", "    series = load_data_to_series(\"output\")\n", "    \n", "    # TODO: replace \"None\" with your implementation to\n", "    #       select the row with view_count greater or equal to 2000 and less than 3000 \n", "    #       i.e., [2000, 3000)\n", "    raise NotImplementedError(\"To be implemented\")\n", "    res = None\n", "    \n", "    # print the result to standard output in the CSV format\n", "    res.to_csv(sys.stdout, encoding='utf-8', header=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["excluded_from_script"]}, "outputs": [], "source": ["q9()"]}, {"cell_type": "markdown", "metadata": {"tags": ["excluded_from_script"]}, "source": ["# DO NOT MODIFY ANYTHING BELOW  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def main():\n", "    parser = argparse.ArgumentParser(\n", "    description=\"Data Analysis\")\n", "    parser.add_argument(\"-r\",\n", "                        metavar='<question_id>',\n", "                        required=False)\n", "    args = parser.parse_args()\n", "    question = args.r\n", "\n", "    if question is None:\n", "        q6()\n", "        q7()\n", "        q8()\n", "        q9()\n", "    elif question == \"q6\":\n", "        q6()\n", "    elif question == \"q7\":\n", "        q7()\n", "    elif question == \"q8\":\n", "        q8()\n", "    elif question == \"q9\":\n", "        q9()\n", "    else:\n", "        print(\"Invalid question\")\n", "        \n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}